"""edit value on Options

Revision ID: 659efe7f1a64
Revises: 6f227085cc19
Create Date: 2025-09-23 00:56:12.558259
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '659efe7f1a64'
down_revision: Union[str, None] = '6f227085cc19'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 1) Normalize existing data (NULL / empty / non-numeric -> '0')
    #    Adjust the regex if you want to allow negatives: '^-?\\d+$'
    op.execute("""
        UPDATE options
        SET value = '0'
        WHERE value IS NULL
           OR btrim(value) = ''
           OR value !~ '^\d+$'
    """)

    # 2) Convert type with USING clause
    op.alter_column(
        'options',
        'value',
        type_=sa.Integer(),
        existing_type=sa.VARCHAR(),
        postgresql_using='value::integer',
        existing_nullable=True  # helps Alembic generate correct SQL
    )

    # 3) Enforce NOT NULL and (optionally) a DB default of 0
    op.alter_column(
        'options',
        'value',
        existing_type=sa.Integer(),
        nullable=False,
        server_default=sa.text('0'),
    )

    # Keep your other change
    op.alter_column(
        'user_lesson_progress',
        'progress_id',
        existing_type=sa.UUID(),
        nullable=False
    )


def downgrade() -> None:
    # Reverse DEFAULT/NOT NULL first
    op.alter_column(
        'options',
        'value',
        existing_type=sa.Integer(),
        nullable=True,
        server_default=None,
    )

    # Cast back to text (simple)
    op.alter_column(
        'options',
        'value',
        type_=sa.VARCHAR(),
        existing_type=sa.Integer(),
        postgresql_using='value::text'
    )

    op.alter_column(
        'user_lesson_progress',
        'progress_id',
        existing_type=sa.UUID(),
        nullable=True
    )
