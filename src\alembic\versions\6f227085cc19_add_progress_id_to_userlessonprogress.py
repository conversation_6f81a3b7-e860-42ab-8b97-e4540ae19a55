"""Add progress_id to UserLessonProgress

Revision ID: 6f227085cc19
Revises: 9fa2efa84886
Create Date: 2025-09-22 17:59:24.712208

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6f227085cc19'
down_revision: Union[str, None] = '9fa2efa84886'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_lesson_progress', sa.Column('progress_id', sa.UUID(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_lesson_progress', 'progress_id')
    # ### end Alembic commands ###
