from fastapi import APIRouter, Depends, HTTPException, Request
from typing import List
from uuid import UUID
from src.routes.deps import SessionDep, CurrentUserUpgrade, get_current_user_upgrade
from src.utils.handle_router_exceptions import handle_router_exceptions
from src.utils.Error_Handling import ErrorCode
from src.schemas.course import (
    CourseCreate, CourseUpdate, CourseRead,
    LessonCreate, LessonUpdate, LessonRead,
    CourseContentCreate, CourseContentUpdate, CourseContentRead, BulkLessonCreate,
    LessonProgressCreate, LessonProgressRead, CompletedLessonsResponse, LessonProgressUpdate
)
from src.services.course import (
    create_course, get_all_courses, get_course_by_id, update_course, delete_course,
    create_lesson, get_lessons_by_course, get_lesson_by_id, update_lesson, delete_lesson, bulk_create_lessons,
    add_course_content, update_course_content, delete_course_content,
    mark_lesson_complete, get_completed_lessons_for_course, update_lesson_progress
)

course_router = APIRouter(prefix="/courses", tags=["Courses"], dependencies=[Depends(get_current_user_upgrade)])


# --------------------
# Course CRUD
# --------------------

@course_router.post("/", response_model=CourseRead)
@handle_router_exceptions
async def create_course_ep(request: Request, data: CourseCreate, db: SessionDep):
    return await create_course(data, db)

@course_router.get("/", response_model=List[CourseRead])
@handle_router_exceptions
async def get_all_courses_ep(request: Request, db: SessionDep):
    return await get_all_courses(db)

@course_router.get("/{course_id}", response_model=CourseRead)
@handle_router_exceptions
async def get_course_ep(request: Request, course_id: UUID, db: SessionDep):
    course = await get_course_by_id(course_id, db)
    if not course:
        raise HTTPException(status_code=404, detail=ErrorCode.COURSE_NOT_FOUND)
    return course

@course_router.put("/{course_id}", response_model=CourseRead)
@handle_router_exceptions
async def update_course_ep(request: Request, course_id: UUID, data: CourseUpdate, db: SessionDep):
    return await update_course(course_id, data, db)

@course_router.delete("/{course_id}", status_code=204)
@handle_router_exceptions
async def delete_course_ep(request: Request, course_id: UUID, db: SessionDep):
    deleted = await delete_course(course_id, db)
    if not deleted:
        raise HTTPException(status_code=404, detail=ErrorCode.COURSE_NOT_FOUND)
    return None


# --------------------
# Lesson CRUD
# --------------------

@course_router.post("/{course_id}/lessons", response_model=LessonRead)
@handle_router_exceptions
async def create_lesson_ep(request: Request, course_id: UUID, data: LessonCreate, db: SessionDep):
    return await create_lesson(course_id, data, db)

@course_router.get("/{course_id}/lessons", response_model=List[LessonRead])
@handle_router_exceptions
async def get_lessons_ep(request: Request, course_id: UUID, db: SessionDep):
    return await get_lessons_by_course(course_id, db)

@course_router.get("/lessons/{lesson_id}", response_model=LessonRead)
@handle_router_exceptions
async def get_lesson_ep(request: Request, lesson_id: UUID, db: SessionDep):
    return await get_lesson_by_id(lesson_id, db)

@course_router.put("/lessons/{lesson_id}", response_model=LessonRead)
@handle_router_exceptions
async def update_lesson_ep(request: Request, lesson_id: UUID, data: LessonUpdate, db: SessionDep):
    return await update_lesson(lesson_id, data, db)

@course_router.delete("/lessons/{lesson_id}", status_code=204)
@handle_router_exceptions
async def delete_lesson_ep(request: Request, lesson_id: UUID, db: SessionDep):
    deleted = await delete_lesson(lesson_id, db)
    if not deleted:
        raise HTTPException(status_code=404, detail=ErrorCode.LESSON_NOT_FOUND)
    return None

@course_router.post("/{course_id}/lessons/bulk", response_model=List[LessonRead])
@handle_router_exceptions
async def bulk_create_lessons_ep(request: Request, course_id: UUID, data: BulkLessonCreate, db: SessionDep):
    return await bulk_create_lessons(course_id, data.lessons, db)

# --------------------
# Course Content Management
# --------------------

@course_router.post("/{course_id}/contents", response_model=CourseContentRead)
@handle_router_exceptions
async def add_content_ep(request: Request, course_id: UUID, data: CourseContentCreate, db: SessionDep):
    return await add_course_content(course_id, data, db)

@course_router.put("/contents/{content_id}", response_model=CourseContentRead)
@handle_router_exceptions
async def update_content_ep(request: Request, content_id: UUID, data: CourseContentUpdate, db: SessionDep):
    return await update_course_content(content_id, data, db)

@course_router.delete("/contents/{content_id}", status_code=204)
@handle_router_exceptions
async def delete_content_ep(request: Request, content_id: UUID, db: SessionDep):
    deleted = await delete_course_content(content_id, db)
    if not deleted:
        raise HTTPException(status_code=404, detail=ErrorCode.CONTENT_NOT_FOUND)
    return None


# --------------------
# Lesson Progress Management
# --------------------

@course_router.post("/lessons/{lesson_id}/complete", response_model=LessonProgressRead)
@handle_router_exceptions
async def complete_lesson_ep(request: Request, lesson_id: UUID, user_id: CurrentUserUpgrade, db: SessionDep):
    """Mark a lesson as completed for the current user."""
    return await mark_lesson_complete(user_id, lesson_id, db)

@course_router.put("/lessons/{progress_id}/progress", response_model=LessonProgressRead)
@handle_router_exceptions
async def update_lesson_progress_ep(request: Request, progress_id: UUID, payload: LessonProgressUpdate, user_id: CurrentUserUpgrade, db: SessionDep):
    """Update the viewed_at timestamp for a lesson."""
    return await update_lesson_progress(user_id, progress_id, payload, db)

@course_router.post("/lessons/complete", response_model=LessonProgressRead)
@handle_router_exceptions
async def complete_lesson_by_id_ep(request: Request, data: LessonProgressCreate, user_id: CurrentUserUpgrade, db: SessionDep):
    """Mark a lesson as completed for the current user using lesson_id in request body."""
    return await mark_lesson_complete(user_id, data.lesson_id, db)

@course_router.get("/{course_id}/completed-lessons", response_model=CompletedLessonsResponse)
@handle_router_exceptions
async def get_completed_lessons_ep(request: Request, course_id: UUID, user_id: CurrentUserUpgrade, db: SessionDep):
    """Get all completed lessons for the current user in a specific course."""
    completed_lessons = await get_completed_lessons_for_course(user_id, course_id, db)
    return CompletedLessonsResponse(
        course_id=course_id,
        completed_lessons=completed_lessons
    )
