
from uuid import UUID

from sqlalchemy import any_, literal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from src.DB.enums import UserRole
from src.utils.logger import AdvancedLogger
from src.DB.models.users import User

logger = AdvancedLogger(name=__name__)


async def get_contain_manger_user(user_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(User).where(
            User.user_id == user_id, User.is_active == True, User.is_deleted == False, User.is_banned == False
        )
    )
    user = result.scalar_one_or_none()
    if (user and (UserRole.CONTENT_MANAGER in user.roles)) or (UserRole.ADMIN in user.roles):
        return user
    return None


async def get_admin(user_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(User).where(
            User.user_id == user_id, User.is_active == True, User.is_deleted == False, User.is_banned == False
        )
    )
    user = result.scalar_one_or_none()
    if (user and (UserRole.ADMIN in user.roles)):
        return user
    return None



