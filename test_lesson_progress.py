#!/usr/bin/env python3
"""
Simple test script to verify lesson progress endpoints work correctly.
This script tests the new lesson completion functionality.
"""

import asyncio
import sys
import os
from uuid import uuid4

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.DB.database import get_sessionmaker
from src.DB.models.assessments import Course, Lesson, CourseContent, UserLessonProgress
from src.DB.models.users import User
from src.services.course import mark_lesson_complete, get_completed_lessons_for_course
from src.schemas.course import LessonProgressCreate


async def test_lesson_progress():
    """Test the lesson progress functionality"""
    print("🧪 Testing Lesson Progress Functionality...")
    
    # Get database session
    session_maker = get_sessionmaker()
    async with session_maker() as db:
        try:
            # Create test data
            print("📝 Creating test data...")
            
            # Create a test course
            test_course = Course(
                course_id=uuid4(),
                title="Test Course for Lesson Progress",
                description="A test course",
                is_active=True
            )
            db.add(test_course)
            
            # Create a test lesson
            test_lesson = Lesson(
                lesson_id=uuid4(),
                title="Test Lesson",
                body_md="This is a test lesson content",
                video_url=None,
                file_url=None
            )
            db.add(test_lesson)
            
            # Create course content linking lesson to course
            course_content = CourseContent(
                content_id=uuid4(),
                course_id=test_course.course_id,
                content_type="lesson",
                lesson_id=test_lesson.lesson_id,
                sequence=1
            )
            db.add(course_content)
            
            await db.commit()
            await db.refresh(test_course)
            await db.refresh(test_lesson)
            
            print(f"✅ Created test course: {test_course.course_id}")
            print(f"✅ Created test lesson: {test_lesson.lesson_id}")
            
            # Test with a mock user ID (you might need to create a real user)
            test_user_id = uuid4()
            print(f"🧑 Using test user ID: {test_user_id}")
            
            # Test 1: Mark lesson as complete
            print("\n🎯 Test 1: Marking lesson as complete...")
            try:
                progress = await mark_lesson_complete(test_user_id, test_lesson.lesson_id, db)
                print(f"✅ Lesson marked as complete: {progress.completed}")
                print(f"✅ Progress viewed_at: {progress.viewed_at}")
            except Exception as e:
                print(f"❌ Error marking lesson complete: {e}")
                return False
            
            # Test 2: Get completed lessons for course
            print("\n🎯 Test 2: Getting completed lessons for course...")
            try:
                completed_lessons = await get_completed_lessons_for_course(
                    test_user_id, test_course.course_id, db
                )
                print(f"✅ Found {len(completed_lessons)} completed lessons")
                if completed_lessons:
                    for lesson_progress in completed_lessons:
                        print(f"   - Lesson: {lesson_progress.lesson.title}")
                        print(f"   - Completed: {lesson_progress.completed}")
                        print(f"   - Viewed at: {lesson_progress.viewed_at}")
            except Exception as e:
                print(f"❌ Error getting completed lessons: {e}")
                return False
            
            # Test 3: Mark same lesson complete again (should update existing record)
            print("\n🎯 Test 3: Marking same lesson complete again...")
            try:
                progress2 = await mark_lesson_complete(test_user_id, test_lesson.lesson_id, db)
                print(f"✅ Lesson marked as complete again: {progress2.completed}")
                print(f"✅ Updated viewed_at: {progress2.viewed_at}")
            except Exception as e:
                print(f"❌ Error marking lesson complete again: {e}")
                return False
            
            print("\n🎉 All tests passed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """Main test function"""
    print("🚀 Starting Lesson Progress Tests...")
    success = await test_lesson_progress()
    
    if success:
        print("\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
