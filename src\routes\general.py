import traceback
from typing import List

from src.services.general import get_all_countries
from src.utils.logger import AdvancedLogger
from fastapi import APIRouter, status, Request, HTTPException, Depends
from src.schemas import user as identity_schemas
from src.utils.handle_router_exceptions import handle_router_exceptions, ErrorResponse
from src.routes.deps import SessionDep, CurrentUserUpgrade, get_api_key
from src.services.auth import get_user
from src.utils.Error_Handling import ErrorCode
from src.DB import enums
from src.DB.enums import enum_to_options
logger = AdvancedLogger(name=__name__)

general_router = APIRouter(
    prefix='/general',
    tags=["General"],
)


@general_router.get("/all-enums", status_code=status.HTTP_200_OK, dependencies=[Depends(get_api_key)])
@handle_router_exceptions
async def get_all_enums(request: Request, db: SessionDep) -> dict:
    """ Get all enums """
    return {
        "UserRole": enum_to_options(enums.UserRole),
        "UserGender": enum_to_options(enums.UserGender),
        "AssessmentType": enum_to_options(enums.AssessmentType),
        "CourseContentType": enum_to_options(enums.CourseContentType),
        "DimensionType": enum_to_options(enums.DimensionType),
        "DimensionCode": enum_to_options(enums.DimensionCode),
        # "ScoringMethod": enum_to_options(enums.ScoringMethod),
        
        }

    

# get user profile
@general_router.get("/countries", 
                        responses={
                            200: {"model": List[identity_schemas.Country], "description": "Countries retrieved successfully"},
                            500: {"model": ErrorResponse, "description": "Internal server error"}
                        },
                    response_model=List[identity_schemas.Country], status_code=status.HTTP_200_OK)
@handle_router_exceptions
async def get_countries(request: Request, db: SessionDep) -> List[identity_schemas.Country]:
    """ Get all countries """
    countries = await get_all_countries(db)
    return [identity_schemas.Country.model_validate(country) for country in countries]


@general_router.get("/test-email", status_code=status.HTTP_200_OK)
@handle_router_exceptions
async def test_email(request: Request,current_user: CurrentUserUpgrade ,db: SessionDep) -> bool:
    """ Test email """
    try:
        from src.utils.Email.email import Email
        from src.schemas.user import UserRead
        user = await get_user(db=db,user_id=str(current_user))
        if not user:
            raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
        user_read = UserRead.model_validate(user)
        email_service = Email(user_read)
        email_service.send_registration_email("0882481612")
        return True
    except Exception as e:
            traceback.print_exc()


