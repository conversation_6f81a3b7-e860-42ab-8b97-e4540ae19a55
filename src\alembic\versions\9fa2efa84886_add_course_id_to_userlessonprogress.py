"""Add course_id to UserLessonProgress

Revision ID: 9fa2efa84886
Revises: 032ad85efba3
Create Date: 2025-09-22 17:34:23.096227

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9fa2efa84886'
down_revision: Union[str, None] = '032ad85efba3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_lesson_progress', sa.Column('course_id', sa.UUID(), nullable=False))
    op.create_foreign_key(None, 'user_lesson_progress', 'courses', ['course_id'], ['course_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_lesson_progress', type_='foreignkey')
    op.drop_column('user_lesson_progress', 'course_id')
    # ### end Alembic commands ###
