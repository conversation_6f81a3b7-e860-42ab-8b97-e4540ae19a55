#!/usr/bin/env python3
"""
Test script to verify ErrorCode consistency across the project.
This script checks that all ErrorCode values are properly defined and used.
"""

import sys
import os
import re
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.Error_Handling import ErrorCode

def test_error_code_enum():
    """Test that ErrorCode enum is properly defined and accessible."""
    print("Testing ErrorCode enum...")
    
    # Test that we can access all error codes
    error_codes = [attr for attr in dir(ErrorCode) if not attr.startswith('_')]
    print(f"Found {len(error_codes)} error codes:")
    
    # Group by category
    categories = {
        'AUTH & LOGIN': [],
        'TOKEN ERRORS': [],
        'USER & SYNC ERRORS': [],
        'ASSESSMENT & QUESTIONS': [],
        'SESSION ERRORS': [],
        'API / SERVER ERRORS': []
    }
    
    # Sample some key error codes to verify they work
    test_codes = [
        'USER_NOT_FOUND',
        'INVALID_CREDENTIALS', 
        'ACCESS_TOKEN_EXPIRED',
        'SESSION_NOT_FOUND',
        'ASSESSMENT_NOT_FOUND',
        'DATABASE_ERROR',
        'INTERNAL_SERVER_ERROR',
        'TOKEN_HAS_EXPIRED',
        'INVALID_TOKEN',
        'REQUEST_TIMED_OUT',
        'COURSE_NOT_FOUND',
        'LESSON_NOT_FOUND',
        'EXPIRED_VERIFICATION_CODE',
        'DUPLICATE_ANSWERS_FOR_QUESTION',
        'INVALID_OPTION_SELECTED',
        'OPTION_DOES_NOT_BELONG_TO_QUESTION',
        'PROGRESS_RECORD_NOT_FOUND',
        'NO_REFRESH_TOKEN_PROVIDED_SIMPLE',
        'INVALID_REFRESH_TOKEN_SIMPLE',
        'COULD_NOT_VALIDATE_API_KEY',
        'NO_PROFILE_PICTURE_UPLOADED'
    ]
    
    print("\nTesting key error codes:")
    for code in test_codes:
        try:
            error_code = getattr(ErrorCode, code)
            print(f"✓ {code}: '{error_code}'")
        except AttributeError:
            print(f"✗ {code}: NOT FOUND")
            return False
    
    print("\nAll error codes are properly defined!")
    return True

def scan_for_hardcoded_strings():
    """Scan for any remaining hardcoded error strings in HTTPException calls."""
    print("\nScanning for hardcoded error strings...")
    
    src_path = Path(__file__).parent / "src"
    issues_found = []
    
    # Patterns to look for
    patterns = [
        r'HTTPException\([^)]*detail\s*=\s*["\'][^"\']*["\']',  # detail="string"
        r'HTTPException\([^)]*detail\s*=\s*f["\'][^"\']*["\']',  # detail=f"string"
    ]
    
    for py_file in src_path.rglob("*.py"):
        if py_file.name.startswith("test_"):
            continue
            
        try:
            content = py_file.read_text(encoding='utf-8')
            for i, line in enumerate(content.split('\n'), 1):
                # Skip commented lines
                stripped_line = line.strip()
                if stripped_line.startswith('#'):
                    continue

                for pattern in patterns:
                    if re.search(pattern, line) and 'ErrorCode.' not in line:
                        # Skip validation exception handler as it uses dynamic strings
                        if 'validation_exception_handler' in py_file.name:
                            continue
                        issues_found.append(f"{py_file.relative_to(src_path)}:{i}: {line.strip()}")
        except Exception as e:
            print(f"Error reading {py_file}: {e}")
    
    if issues_found:
        print("Found potential hardcoded error strings:")
        for issue in issues_found:
            print(f"  ⚠️  {issue}")
        return False
    else:
        print("✓ No hardcoded error strings found!")
        return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("ErrorCode Consistency Test")
    print("=" * 60)
    
    success = True
    
    # Test 1: ErrorCode enum
    success &= test_error_code_enum()
    
    # Test 2: Scan for hardcoded strings
    success &= scan_for_hardcoded_strings()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed! ErrorCode system is consistent.")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
