from enum import Enum
from typing import List, Dict

class UserGender(str, Enum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    OTHER = 'OTHER'


class UserRole(str, Enum):
    USER = "USER"
    PARENT = "PARENT"
    ADMIN = "ADMIN"
    CONTENT_MANAGER = "CONTENT_MANAGER"


class AuthProvider(str, Enum):
    LOCAL = "LOCAL"
    GOOGLE = "GOOGLE"
    FACEBOOK = "FACEBOOK"

class AssessmentType(str, Enum):
    MOCK = "MOCK"
    REAL = "REAL"
    QUIZ = "QUIZ"
    
class CourseContentType(str, Enum):
    TEXT = "text"
    VIDEO = "video"
    ASSESSMENT = "assessment"


class DeviceType(Enum):
    PC = 'PC'
    MOBILE = 'MOBILE'
    UNKNOWN = 'UNKNOWN'


class TokenType(Enum):
    ACCESS_TOKEN = 'ACCESS_TOKEN'
    REFRESH_TOKEN = 'REFRESH_TOKEN'

class ScoringMethod(str, Enum):
    TYPE = "type"
    TRAIT = "trait"
    CUSTOM = "custom"

class DimensionType(str, Enum):
    TRUE_FALSE = "TRUE_FALSE"
    SINGLE_CHOICE = "SINGLE_CHOICE"
    MULTIPLE_CHOICE = "MULTIPLE_CHOICE"

class DimensionCode(str, Enum):
    TF = "TF"
    SC = "SC"
    MC = "MC"



def enum_to_options(enum_class: Enum) -> List[Dict[str, str]]:
    return [{"label": item.value, "value": item.value} for item in enum_class]
